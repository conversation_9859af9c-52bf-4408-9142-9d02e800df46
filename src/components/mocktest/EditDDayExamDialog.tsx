import { useState, useEffect } from "react";
import { Calendar as CalendarIcon, Clock, Target, Save, X } from "lucide-react";
import { format } from "date-fns";
import { DDayExam, dDayStorage, ChapterProgress } from "@/utils/mockTestLocalStorage";
import { TestCategory } from "@/types/mockTest";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/components/ui/use-toast";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogHeader,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Switch } from "@/components/ui/switch";
import { cn } from "@/lib/utils";
import { ChapterProgressEditor } from "./ChapterProgressEditor";

interface EditDDayExamDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  exam: DDayExam | null;
  categories: TestCategory[];
  onSave: () => void;
  userId: string;
}

export function EditDDayExamDialog({
  open,
  onOpenChange,
  exam,
  categories,
  onSave,
  userId,
}: EditDDayExamDialogProps) {
  const [formData, setFormData] = useState({
    name: "",
    date: new Date(),
    time: "09:00",
    category: "",
    description: "",
    priority: "medium" as DDayExam['priority'],
    reminderEnabled: true,
    studyHours: 0,
    chapters: [] as ChapterProgress[],
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form data when exam changes
  useEffect(() => {
    if (exam) {
      setFormData({
        name: exam.name,
        date: new Date(exam.date),
        time: exam.time,
        category: exam.category || "",
        description: exam.description || "",
        priority: exam.priority,
        reminderEnabled: exam.reminderSettings.enabled,
        studyHours: exam.preparationData.studyHours,
        chapters: [...exam.preparationData.chapters],
      });
    } else {
      // Reset form for new exam
      setFormData({
        name: "",
        date: new Date(),
        time: "09:00",
        category: "",
        description: "",
        priority: "medium",
        reminderEnabled: true,
        studyHours: 0,
        chapters: [],
      });
    }
  }, [exam]);

  const handleSave = async () => {
    if (!formData.name.trim()) {
      toast({
        title: "Error",
        description: "Exam name is required",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const updatedExam: DDayExam = {
        ...exam!,
        name: formData.name.trim(),
        date: format(formData.date, "yyyy-MM-dd"),
        time: formData.time,
        category: formData.category || undefined,
        description: formData.description || undefined,
        priority: formData.priority,
        reminderSettings: {
          ...exam!.reminderSettings,
          enabled: formData.reminderEnabled,
        },
        preparationData: {
          studyHours: formData.studyHours,
          chapters: formData.chapters,
          totalTopics: formData.chapters.map(ch => ch.chapterName), // Keep for backward compatibility
        },
        updatedAt: new Date().toISOString(),
      };

      const success = dDayStorage.update(userId, exam!.id, updatedExam);
      
      if (success) {
        toast({
          title: "Success",
          description: "Exam updated successfully",
        });
        onSave();
        onOpenChange(false);
      } else {
        throw new Error("Failed to update exam");
      }
    } catch (error) {
      console.error('Error updating exam:', error);
      toast({
        title: "Error",
        description: "Failed to update exam",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!exam) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Target className="h-5 w-5 text-violet-600" />
            Edit Exam
          </DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Left Column - Basic Info */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Exam Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter exam name"
              />
            </div>

            <div className="grid grid-cols-2 gap-3">
              <div className="space-y-2">
                <Label>Date *</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !formData.date && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formData.date ? format(formData.date, "PPP") : "Pick a date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={formData.date}
                      onSelect={(date) => date && setFormData(prev => ({ ...prev, date }))}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="space-y-2">
                <Label htmlFor="time">Time</Label>
                <Input
                  id="time"
                  type="time"
                  value={formData.time}
                  onChange={(e) => setFormData(prev => ({ ...prev, time: e.target.value }))}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-3">
              <div className="space-y-2">
                <Label>Category</Label>
                <Select
                  value={formData.category}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">No Category</SelectItem>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Priority</Label>
                <Select
                  value={formData.priority}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, priority: value as DDayExam['priority'] }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="critical">Critical</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Add exam description..."
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="studyHours">Study Hours</Label>
              <Input
                id="studyHours"
                type="number"
                min="0"
                step="0.5"
                value={formData.studyHours}
                onChange={(e) => setFormData(prev => ({ ...prev, studyHours: parseFloat(e.target.value) || 0 }))}
                placeholder="0"
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="reminders"
                checked={formData.reminderEnabled}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, reminderEnabled: checked }))}
              />
              <Label htmlFor="reminders">Enable Reminders</Label>
            </div>
          </div>

          {/* Right Column - Chapter Progress */}
          <div>
            <ChapterProgressEditor
              chapters={formData.chapters}
              onChaptersChange={(chapters) => setFormData(prev => ({ ...prev, chapters }))}
              className="h-full"
            />
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isSubmitting}
          >
            <X className="h-4 w-4 mr-2" />
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={isSubmitting}
            className="bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700"
          >
            <Save className="h-4 w-4 mr-2" />
            {isSubmitting ? "Saving..." : "Save Changes"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
