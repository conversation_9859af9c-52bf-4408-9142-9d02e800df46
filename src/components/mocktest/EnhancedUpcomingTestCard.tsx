import { useState } from "react";
import { motion } from "framer-motion";
import { 
  Calendar, 
  Clock, 
  Edit2, 
  Trash2, 
  ExternalLink,
  BookOpen,
  Target,
  Bell,
  BellOff,
  MoreHorizontal,
  CheckCircle,
  AlertTriangle,
  Timer,
  TrendingUp
} from "lucide-react";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { UpcomingTest, TestCategory } from "@/types/mockTest";
import { DDayExam } from "@/utils/mockTestLocalStorage";
import { cn } from "@/lib/utils";
import { format } from "date-fns";

interface EnhancedUpcomingTestCardProps {
  test: UpcomingTest;
  dDayExam?: DDayExam;
  category?: TestCategory;
  onEdit: (test: UpcomingTest) => void;
  onDelete: (testId: string) => void;
  onCreateMockTest?: (test: UpcomingTest) => void;
  onToggleNotification?: (testId: string, enabled: boolean) => void;
}

export function EnhancedUpcomingTestCard({
  test,
  dDayExam,
  category,
  onEdit,
  onDelete,
  onCreateMockTest,
  onToggleNotification
}: EnhancedUpcomingTestCardProps) {
  const [isHovered, setIsHovered] = useState(false);

  // Calculate days left
  const daysLeft = test.daysLeft || 0;
  
  // Get urgency styling
  const getUrgencyStyle = (days: number) => {
    if (days <= 1) return {
      border: "border-red-200 dark:border-red-800",
      bg: "from-red-50 to-red-100 dark:from-red-950/30 dark:to-red-900/20",
      text: "text-red-700 dark:text-red-400",
      badge: "bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400"
    };
    if (days <= 3) return {
      border: "border-orange-200 dark:border-orange-800",
      bg: "from-orange-50 to-orange-100 dark:from-orange-950/30 dark:to-orange-900/20",
      text: "text-orange-700 dark:text-orange-400",
      badge: "bg-orange-100 text-orange-700 dark:bg-orange-900/30 dark:text-orange-400"
    };
    if (days <= 7) return {
      border: "border-yellow-200 dark:border-yellow-800",
      bg: "from-yellow-50 to-yellow-100 dark:from-yellow-950/30 dark:to-yellow-900/20",
      text: "text-yellow-700 dark:text-yellow-400",
      badge: "bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400"
    };
    return {
      border: "border-green-200 dark:border-green-800",
      bg: "from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/20",
      text: "text-green-700 dark:text-green-400",
      badge: "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400"
    };
  };

  const urgencyStyle = getUrgencyStyle(daysLeft);

  // Calculate preparation progress if D-Day exam exists
  const preparationProgress = dDayExam ? 
    (dDayExam.preparationData.completedTopics.length / Math.max(dDayExam.preparationData.totalTopics.length, 1)) * 100 : 0;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -4, scale: 1.02 }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      className="h-full"
    >
      <Card className={cn(
        "h-full overflow-hidden shadow-lg hover:shadow-xl transition-all duration-500 group relative",
        "bg-gradient-to-br",
        urgencyStyle.bg,
        urgencyStyle.border
      )}>
        {/* Priority Indicator */}
        {dDayExam && (
          <div className={cn(
            "absolute top-0 left-0 w-1 h-full",
            dDayExam.priority === 'critical' && "bg-red-500",
            dDayExam.priority === 'high' && "bg-orange-500",
            dDayExam.priority === 'medium' && "bg-yellow-500",
            dDayExam.priority === 'low' && "bg-green-500"
          )} />
        )}

        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-lg truncate mb-1">
                {test.name}
              </h3>
              {category && (
                <Badge 
                  variant="secondary" 
                  className="text-xs"
                  style={{ backgroundColor: category.color + '20', color: category.color }}
                >
                  {category.name}
                </Badge>
              )}
            </div>
            
            <div className="flex items-center gap-2 ml-2">
              {/* Notification Status */}
              <motion.div
                animate={isHovered ? { scale: 1.1 } : { scale: 1 }}
                className={cn(
                  "p-1 rounded-full",
                  test.isNotificationEnabled 
                    ? "bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400" 
                    : "bg-gray-100 text-gray-400 dark:bg-gray-800 dark:text-gray-500"
                )}
              >
                {test.isNotificationEnabled ? (
                  <Bell className="h-3 w-3" />
                ) : (
                  <BellOff className="h-3 w-3" />
                )}
              </motion.div>

              {/* Actions Menu */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => onEdit(test)}>
                    <Edit2 className="h-4 w-4 mr-2" />
                    Edit Test
                  </DropdownMenuItem>
                  
                  {onToggleNotification && (
                    <DropdownMenuItem 
                      onClick={() => onToggleNotification(test.id, !test.isNotificationEnabled)}
                    >
                      {test.isNotificationEnabled ? (
                        <>
                          <BellOff className="h-4 w-4 mr-2" />
                          Disable Notifications
                        </>
                      ) : (
                        <>
                          <Bell className="h-4 w-4 mr-2" />
                          Enable Notifications
                        </>
                      )}
                    </DropdownMenuItem>
                  )}

                  {test.testPaperUrl && (
                    <DropdownMenuItem asChild>
                      <a href={test.testPaperUrl} target="_blank" rel="noopener noreferrer">
                        <ExternalLink className="h-4 w-4 mr-2" />
                        View Test Paper
                      </a>
                    </DropdownMenuItem>
                  )}

                  {onCreateMockTest && (
                    <>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => onCreateMockTest(test)}>
                        <Target className="h-4 w-4 mr-2" />
                        Create Mock Test
                      </DropdownMenuItem>
                    </>
                  )}

                  <DropdownMenuSeparator />
                  <DropdownMenuItem 
                    onClick={() => onDelete(test.id)}
                    className="text-red-600 dark:text-red-400"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete Test
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Date and Time */}
          <div className="grid grid-cols-2 gap-3">
            <div className="flex items-center gap-2 text-sm">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="font-medium">
                {format(new Date(test.date), 'MMM d, yyyy')}
              </span>
            </div>
            
            {test.time && (
              <div className="flex items-center gap-2 text-sm">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">{test.time}</span>
              </div>
            )}
          </div>

          {/* Days Left Indicator */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {daysLeft <= 3 ? (
                <AlertTriangle className={cn("h-4 w-4", urgencyStyle.text)} />
              ) : (
                <Timer className={cn("h-4 w-4", urgencyStyle.text)} />
              )}
              <span className={cn("font-semibold", urgencyStyle.text)}>
                {daysLeft === 0 ? "Today!" : 
                 daysLeft === 1 ? "Tomorrow" : 
                 `${daysLeft} days left`}
              </span>
            </div>
            
            <Badge className={urgencyStyle.badge}>
              {daysLeft <= 1 ? "URGENT" : 
               daysLeft <= 3 ? "SOON" : 
               daysLeft <= 7 ? "THIS WEEK" : "UPCOMING"}
            </Badge>
          </div>

          {/* Preparation Progress (if D-Day exam exists) */}
          {dDayExam && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Preparation Progress</span>
                <span className="font-medium">{Math.round(preparationProgress)}%</span>
              </div>
              <Progress value={preparationProgress} className="h-2" />
              
              <div className="flex items-center justify-between text-xs text-muted-foreground">
                <span>{dDayExam.preparationData.completedTopics.length} / {dDayExam.preparationData.totalTopics.length} topics</span>
                <span>{dDayExam.preparationData.studyHours}h studied</span>
              </div>
            </div>
          )}

          {/* Syllabus Topics */}
          {test.syllabus && test.syllabus.length > 0 && (
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <BookOpen className="h-4 w-4" />
                <span>Syllabus ({test.syllabus.length} topics)</span>
              </div>
              <div className="flex flex-wrap gap-1">
                {test.syllabus.slice(0, 3).map((topic, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {topic}
                  </Badge>
                ))}
                {test.syllabus.length > 3 && (
                  <Badge variant="outline" className="text-xs">
                    +{test.syllabus.length - 3} more
                  </Badge>
                )}
              </div>
            </div>
          )}

          {/* Confidence Level (if D-Day exam exists) */}
          {dDayExam && (
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Confidence Level</span>
              <div className="flex items-center gap-1">
                {[1, 2, 3, 4, 5].map((level) => (
                  <div
                    key={level}
                    className={cn(
                      "w-3 h-3 rounded-full",
                      level <= dDayExam.preparationData.confidenceLevel
                        ? "bg-violet-500"
                        : "bg-gray-200 dark:bg-gray-700"
                    )}
                  />
                ))}
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2 pt-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onEdit(test)}
              className="flex-1"
            >
              <Edit2 className="h-4 w-4 mr-2" />
              Edit
            </Button>
            
            {onCreateMockTest && (
              <Button
                variant="default"
                size="sm"
                onClick={() => onCreateMockTest(test)}
                className="flex-1 bg-violet-600 hover:bg-violet-700"
              >
                <Target className="h-4 w-4 mr-2" />
                Mock Test
              </Button>
            )}
          </div>
        </CardContent>

        {/* Hover Effect Overlay */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: isHovered ? 0.05 : 0 }}
          className="absolute inset-0 bg-gradient-to-br from-violet-500 to-purple-500 pointer-events-none"
        />
      </Card>
    </motion.div>
  );
}
