import { DDayExam } from "@/utils/mockTestLocalStorage";

// Notification types
export interface ExamNotification {
  id: string;
  examId: string;
  type: 'reminder' | 'preparation' | 'urgent' | 'motivation';
  title: string;
  message: string;
  scheduledFor: string; // ISO date string
  isRead: boolean;
  createdAt: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
}

// Smart notification system
export class ExamNotificationSystem {
  private static instance: ExamNotificationSystem;
  private notifications: Map<string, ExamNotification[]> = new Map();

  static getInstance(): ExamNotificationSystem {
    if (!ExamNotificationSystem.instance) {
      ExamNotificationSystem.instance = new ExamNotificationSystem();
    }
    return ExamNotificationSystem.instance;
  }

  // Generate smart notifications for an exam
  generateNotifications(exam: DDayExam): ExamNotification[] {
    const notifications: ExamNotification[] = [];
    const now = new Date();
    const examDate = new Date(exam.date);
    const daysUntil = Math.ceil((examDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

    // Reminder notifications based on intervals
    exam.reminderSettings.intervals.forEach((interval: number) => {
      const reminderDate = new Date(examDate);
      reminderDate.setDate(reminderDate.getDate() - interval);

      if (reminderDate > now) {
        notifications.push({
          id: `reminder_${exam.id}_${interval}`,
          examId: exam.id,
          type: 'reminder',
          title: `Exam Reminder: ${exam.name}`,
          message: `Your exam "${exam.name}" is in ${interval} day${interval > 1 ? 's' : ''}. Time to prepare!`,
          scheduledFor: reminderDate.toISOString(),
          isRead: false,
          createdAt: new Date().toISOString(),
          priority: interval <= 1 ? 'critical' : interval <= 3 ? 'high' : 'medium'
        });
      }
    });

    // Preparation-based notifications
    const completionRate = exam.preparationData.completedTopics.length / Math.max(exam.preparationData.totalTopics.length, 1);

    if (daysUntil <= 7 && completionRate < 0.5) {
      notifications.push({
        id: `preparation_${exam.id}_low`,
        examId: exam.id,
        type: 'preparation',
        title: `Preparation Alert: ${exam.name}`,
        message: `You've only completed ${Math.round(completionRate * 100)}% of topics. Consider increasing study time.`,
        scheduledFor: new Date().toISOString(),
        isRead: false,
        createdAt: new Date().toISOString(),
        priority: 'high'
      });
    }

    // Confidence-based notifications
    if (daysUntil <= 3 && exam.preparationData.confidenceLevel < 3) {
      notifications.push({
        id: `confidence_${exam.id}_low`,
        examId: exam.id,
        type: 'preparation',
        title: `Confidence Boost Needed: ${exam.name}`,
        message: `Your confidence level is ${exam.preparationData.confidenceLevel}/5. Consider reviewing key topics.`,
        scheduledFor: new Date().toISOString(),
        isRead: false,
        createdAt: new Date().toISOString(),
        priority: 'medium'
      });
    }

    // Motivational notifications
    if (completionRate >= 0.8 && exam.preparationData.confidenceLevel >= 4) {
      notifications.push({
        id: `motivation_${exam.id}_good`,
        examId: exam.id,
        type: 'motivation',
        title: `Great Progress: ${exam.name}`,
        message: `You're well prepared! ${Math.round(completionRate * 100)}% complete with high confidence. Keep it up!`,
        scheduledFor: new Date().toISOString(),
        isRead: false,
        createdAt: new Date().toISOString(),
        priority: 'low'
      });
    }

    return notifications;
  }

  // Get notifications for a user
  getNotifications(userId: string): ExamNotification[] {
    return this.notifications.get(userId) || [];
  }

  // Add notification
  addNotification(userId: string, notification: ExamNotification): void {
    const userNotifications = this.notifications.get(userId) || [];
    userNotifications.push(notification);
    this.notifications.set(userId, userNotifications);
  }

  // Mark notification as read
  markAsRead(userId: string, notificationId: string): void {
    const userNotifications = this.notifications.get(userId) || [];
    const notification = userNotifications.find(n => n.id === notificationId);
    if (notification) {
      notification.isRead = true;
    }
  }

  // Get unread count
  getUnreadCount(userId: string): number {
    const userNotifications = this.notifications.get(userId) || [];
    return userNotifications.filter(n => !n.isRead).length;
  }

  // Clear old notifications
  clearOldNotifications(userId: string, daysOld: number = 30): void {
    const userNotifications = this.notifications.get(userId) || [];
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);

    const filteredNotifications = userNotifications.filter(n =>
      new Date(n.createdAt) > cutoffDate
    );

    this.notifications.set(userId, filteredNotifications);
  }
}

// Exam preparation insights
export interface PreparationInsight {
  type: 'strength' | 'weakness' | 'recommendation' | 'warning';
  title: string;
  description: string;
  actionable: boolean;
  priority: 'low' | 'medium' | 'high';
  examId?: string;
}

export class ExamInsightsEngine {
  static generateInsights(exams: DDayExam[]): PreparationInsight[] {
    const insights: PreparationInsight[] = [];
    const now = new Date();

    // Analyze each exam
    exams.forEach(exam => {
      const examDate = new Date(exam.date);
      const daysUntil = Math.ceil((examDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      const completionRate = exam.preparationData.completedTopics.length / Math.max(exam.preparationData.totalTopics.length, 1);

      // Time management insights
      if (daysUntil <= 7 && completionRate < 0.3) {
        insights.push({
          type: 'warning',
          title: 'Critical Preparation Gap',
          description: `${exam.name} is in ${daysUntil} days but only ${Math.round(completionRate * 100)}% prepared. Immediate action needed.`,
          actionable: true,
          priority: 'high',
          examId: exam.id
        });
      }

      // Study efficiency insights
      if (exam.preparationData.studyHours > 0) {
        const hoursPerTopic = exam.preparationData.studyHours / Math.max(exam.preparationData.completedTopics.length, 1);
        if (hoursPerTopic > 5) {
          insights.push({
            type: 'recommendation',
            title: 'Study Efficiency Opportunity',
            description: `You're spending ${hoursPerTopic.toFixed(1)} hours per topic for ${exam.name}. Consider more focused study methods.`,
            actionable: true,
            priority: 'medium',
            examId: exam.id
          });
        }
      }

      // Confidence vs preparation mismatch
      if (completionRate >= 0.8 && exam.preparationData.confidenceLevel <= 2) {
        insights.push({
          type: 'recommendation',
          title: 'Confidence Building Needed',
          description: `You've completed most topics for ${exam.name} but confidence is low. Practice tests might help.`,
          actionable: true,
          priority: 'medium',
          examId: exam.id
        });
      }

      // Strengths identification
      if (completionRate >= 0.9 && exam.preparationData.confidenceLevel >= 4) {
        insights.push({
          type: 'strength',
          title: 'Excellent Preparation',
          description: `${exam.name} shows outstanding preparation with ${Math.round(completionRate * 100)}% completion and high confidence.`,
          actionable: false,
          priority: 'low',
          examId: exam.id
        });
      }
    });

    // Cross-exam insights
    const upcomingExams = exams.filter(exam => {
      const daysUntil = Math.ceil((new Date(exam.date).getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      return daysUntil >= 0 && daysUntil <= 30;
    });

    if (upcomingExams.length > 3) {
      insights.push({
        type: 'warning',
        title: 'Heavy Exam Schedule',
        description: `You have ${upcomingExams.length} exams in the next 30 days. Consider prioritizing by importance and difficulty.`,
        actionable: true,
        priority: 'high'
      });
    }

    // Study pattern insights
    const totalStudyHours = exams.reduce((sum, exam) => sum + exam.preparationData.studyHours, 0);
    const avgStudyHours = totalStudyHours / Math.max(exams.length, 1);

    if (avgStudyHours < 5 && upcomingExams.length > 0) {
      insights.push({
        type: 'recommendation',
        title: 'Increase Study Time',
        description: `Average study time is ${avgStudyHours.toFixed(1)} hours per exam. Consider increasing daily study time.`,
        actionable: true,
        priority: 'medium'
      });
    }

    return insights.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }

  // Generate study schedule recommendations
  static generateStudySchedule(exam: DDayExam): { date: string; topics: string[]; hours: number }[] {
    const now = new Date();
    const examDate = new Date(exam.date);
    const daysUntil = Math.ceil((examDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

    const remainingTopics = exam.preparationData.totalTopics.filter(
      (topic: string) => !exam.preparationData.completedTopics.includes(topic)
    );

    if (remainingTopics.length === 0 || daysUntil <= 0) {
      return [];
    }

    const schedule: { date: string; topics: string[]; hours: number }[] = [];
    const topicsPerDay = Math.ceil(remainingTopics.length / Math.max(daysUntil - 1, 1)); // Leave last day for review
    const hoursPerDay = Math.min(8, Math.max(2, Math.ceil(remainingTopics.length * 2 / daysUntil)));

    for (let day = 0; day < daysUntil - 1; day++) {
      const currentDate = new Date(now);
      currentDate.setDate(currentDate.getDate() + day);

      const startIndex = day * topicsPerDay;
      const endIndex = Math.min(startIndex + topicsPerDay, remainingTopics.length);
      const dayTopics = remainingTopics.slice(startIndex, endIndex);

      if (dayTopics.length > 0) {
        schedule.push({
          date: currentDate.toISOString().split('T')[0],
          topics: dayTopics,
          hours: hoursPerDay
        });
      }
    }

    // Add review day
    if (daysUntil > 1) {
      const reviewDate = new Date(examDate);
      reviewDate.setDate(reviewDate.getDate() - 1);

      schedule.push({
        date: reviewDate.toISOString().split('T')[0],
        topics: ['Review all topics', 'Practice tests', 'Final preparation'],
        hours: 4
      });
    }

    return schedule;
  }
}